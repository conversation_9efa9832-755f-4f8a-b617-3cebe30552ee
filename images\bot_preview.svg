<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиенты -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0f;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="neonGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff0080;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8a2be2;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="coinGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffaa00;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#ff6600;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc4400;stop-opacity:1" />
    </radialGradient>
    
    <!-- Фильтры для свечения -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Фон -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  
  <!-- Декоративные линии -->
  <line x1="0" y1="50" x2="400" y2="50" stroke="url(#neonGradient)" stroke-width="2" opacity="0.6" filter="url(#glow)"/>
  <line x1="0" y1="250" x2="400" y2="250" stroke="url(#neonGradient)" stroke-width="2" opacity="0.6" filter="url(#glow)"/>
  
  <!-- Декоративные элементы по углам -->
  <polygon points="0,0 30,0 0,30" fill="#00ffff" opacity="0.3"/>
  <polygon points="400,0 370,0 400,30" fill="#ff0080" opacity="0.3"/>
  <polygon points="0,300 30,300 0,270" fill="#8a2be2" opacity="0.3"/>
  <polygon points="400,300 370,300 400,270" fill="#39ff14" opacity="0.3"/>
  
  <!-- Центральная композиция -->
  <!-- Большая монета в центре -->
  <circle cx="200" cy="150" r="60" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="3" filter="url(#strongGlow)"/>
  <circle cx="200" cy="150" r="45" fill="none" stroke="#ffdd00" stroke-width="2" opacity="0.8"/>
  <text x="200" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#ffffff">$</text>
  
  <!-- Маленькие монеты вокруг -->
  <circle cx="120" cy="100" r="20" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="2" opacity="0.8"/>
  <text x="120" y="107" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">₿</text>
  
  <circle cx="280" cy="100" r="20" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="2" opacity="0.8"/>
  <text x="280" y="107" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">Ξ</text>
  
  <circle cx="120" cy="200" r="20" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="2" opacity="0.8"/>
  <text x="120" y="207" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">USDT</text>
  
  <circle cx="280" cy="200" r="20" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="2" opacity="0.8"/>
  <text x="280" y="207" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">TRX</text>
  
  <!-- Логотип UniQPaid -->
  <text x="200" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#neonGradient)" filter="url(#glow)">UniQPaid</text>
  
  <!-- Подзаголовок -->
  <text x="200" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#00ffff" filter="url(#glow)">Crypto Rewards Platform</text>
  
  <!-- Декоративные точки -->
  <circle cx="50" cy="150" r="3" fill="#00ffff" opacity="0.6"/>
  <circle cx="350" cy="150" r="3" fill="#ff0080" opacity="0.6"/>
  <circle cx="200" cy="80" r="2" fill="#8a2be2" opacity="0.8"/>
  <circle cx="200" cy="220" r="2" fill="#39ff14" opacity="0.8"/>
  
  <!-- Анимированные элементы (статичные в SVG) -->
  <rect x="30" y="140" width="40" height="20" rx="10" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.4"/>
  <rect x="330" y="140" width="40" height="20" rx="10" fill="none" stroke="#ff0080" stroke-width="2" opacity="0.4"/>
  
  <!-- Дополнительные неоновые акценты -->
  <path d="M 60 60 L 80 60 L 70 80 Z" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.5"/>
  <path d="M 340 60 L 320 60 L 330 80 Z" fill="none" stroke="#ff0080" stroke-width="2" opacity="0.5"/>
  <path d="M 60 240 L 80 240 L 70 220 Z" fill="none" stroke="#8a2be2" stroke-width="2" opacity="0.5"/>
  <path d="M 340 240 L 320 240 L 330 220 Z" fill="none" stroke="#39ff14" stroke-width="2" opacity="0.5"/>
</svg>
