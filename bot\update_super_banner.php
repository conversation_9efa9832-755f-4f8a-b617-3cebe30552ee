<?php
/**
 * update_super_banner.php
 * Быстрое обновление супер-баннера бота
 */

require_once __DIR__ . '/config.php';

// Получаем chat_id из параметров или используем тестовый
$chatId = $_GET['chat_id'] ?? null;

if (!$chatId) {
    die("❌ Укажите chat_id в параметре: ?chat_id=YOUR_CHAT_ID");
}

// URL новой супер-картинки с timestamp для обхода кэша
$logoUrl = 'https://app.uniqpaid.com/test2/images/bot_welcome_super_banner.png?' . time();

// Сообщение для теста
$message = "🎨 <b>Обновление супер-баннера бота!</b>\n\n";
$message .= "✨ Тестируем новую версию приветственного баннера.\n";
$message .= "🚀 Файл: bot_welcome_super_banner.png\n";
$message .= "⏰ Время обновления: " . date('Y-m-d H:i:s');

// Клавиатура
$keyboard = [
    'inline_keyboard' => [
        [
            [
                'text' => '🎉 Супер-баннер обновлен!',
                'callback_data' => 'super_banner_updated'
            ]
        ],
        [
            [
                'text' => '🚀 Запустить приложение',
                'web_app' => ['url' => WEBAPP_URL]
            ]
        ],
        [
            [
                'text' => '💰 Мой баланс',
                'callback_data' => 'my_balance'
            ],
            [
                'text' => '👥 Друзья',
                'callback_data' => 'invite_friends'
            ]
        ]
    ]
];

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Обновление супер-баннера бота</title>";
echo "<style>body{font-family:Arial;margin:20px;background:#0a0a0a;color:white;} .success{color:#00ff00;} .error{color:#ff4444;} .info{color:#00ffff;} .banner{background:linear-gradient(45deg,#1a1a2e,#16213e,#533483);padding:20px;border-radius:10px;margin:20px 0;}</style>";
echo "</head><body>\n";

echo "<div class='banner'>\n";
echo "<h1>🎨 Обновление супер-баннера Telegram бота</h1>\n";
echo "</div>\n";

echo "<p class='info'>📤 Отправляем обновленный супер-баннер...</p>\n";
echo "<p>🖼️ URL: <code>{$logoUrl}</code></p>\n";
echo "<p>👤 Chat ID: <code>{$chatId}</code></p>\n";

// Логируем попытку
botLog("INFO: Попытка обновления супер-баннера для chat_id: {$chatId}");
botLog("INFO: URL картинки: {$logoUrl}");

// Отправляем картинку
$result = sendPhoto($chatId, $logoUrl, $message, $keyboard);

if ($result) {
    echo "<div class='banner'>\n";
    echo "<p class='success'>🎉 <strong>Успех!</strong> Супер-баннер успешно отправлен!</p>\n";
    echo "</div>\n";
    
    // Получаем file_id для будущего использования
    if (isset($result['photo']) && is_array($result['photo'])) {
        $fileId = end($result['photo'])['file_id']; // Берем самое большое разрешение
        echo "<p class='info'>🆔 File ID: <code>{$fileId}</code></p>\n";
        echo "<p class='info'>💡 Этот file_id можно использовать для быстрой отправки без URL</p>\n";
        botLog("SUCCESS: Супер-баннер отправлен. File ID: {$fileId}");
    }
    
    echo "<p class='success'>✅ Теперь можете протестировать бота командой /start</p>\n";
    echo "<p class='success'>✅ Новый супер-баннер должен появиться в приветственном сообщении!</p>\n";
    
} else {
    echo "<div class='banner'>\n";
    echo "<p class='error'>❌ <strong>Ошибка!</strong> Не удалось отправить супер-баннер.</p>\n";
    echo "</div>\n";
    
    echo "<p class='error'>Возможные причины:</p>\n";
    echo "<ul>\n";
    echo "<li>❌ Неверный chat_id</li>\n";
    echo "<li>❌ Картинка bot_welcome_super_banner.png недоступна по URL</li>\n";
    echo "<li>❌ Проблемы с Telegram API</li>\n";
    echo "<li>❌ Неподдерживаемый формат файла</li>\n";
    echo "</ul>\n";
    
    botLog("ERROR: Не удалось отправить супер-баннер для chat_id: {$chatId}");
    
    // Пробуем отправить текстовое сообщение
    echo "<p class='info'>🔄 Пробуем отправить текстовое сообщение...</p>\n";
    $textResult = sendMessage($chatId, "🧪 Тест связи с ботом\n\n🎨 Супер-баннер временно недоступен, но бот работает!\n\n⏰ " . date('Y-m-d H:i:s'));
    
    if ($textResult) {
        echo "<p class='success'>✅ Текстовое сообщение отправлено. Проблема именно с картинкой.</p>\n";
    } else {
        echo "<p class='error'>❌ Даже текстовое сообщение не отправилось. Проверьте chat_id и настройки бота.</p>\n";
    }
}

// Дополнительная информация
echo "<hr style='border-color:#333;'>\n";
echo "<h3 class='info'>📋 Информация о супер-баннере</h3>\n";

echo "<p><strong>Файл:</strong> bot_welcome_super_banner.png</p>\n";
echo "<p><strong>Расположение:</strong> images/bot_welcome_super_banner.png</p>\n";
echo "<p><strong>Прямая ссылка:</strong> <a href='https://app.uniqpaid.com/test2/images/bot_welcome_super_banner.png' target='_blank' style='color:#00ffff;'>Открыть картинку</a></p>\n";

echo "<h3 class='info'>🔧 Устранение проблем</h3>\n";
echo "<ul>\n";
echo "<li>✅ Убедитесь, что файл bot_welcome_super_banner.png загружен в папку images/</li>\n";
echo "<li>✅ Проверьте, что файл в формате PNG (не SVG!)</li>\n";
echo "<li>✅ Размер файла не должен превышать 10MB</li>\n";
echo "<li>✅ Убедитесь, что у вас правильный chat_id</li>\n";
echo "</ul>\n";

echo "<h3 class='info'>⚡ Быстрые действия</h3>\n";
echo "<p><a href='?' style='color:#00ffff;'>🔄 Повторить отправку</a></p>\n";
echo "<p><a href='fix_bot.php' style='color:#00ffff;'>🔧 Диагностика бота</a></p>\n";
echo "<p><a href='diagnose.php' style='color:#00ffff;'>🔍 Подробная диагностика</a></p>\n";

echo "<h3 class='info'>📝 Как получить chat_id</h3>\n";
echo "<ol>\n";
echo "<li>Отправьте любое сообщение боту @" . BOT_USERNAME . "</li>\n";
echo "<li>Посмотрите логи бота в файле <code>bot/bot.log</code></li>\n";
echo "<li>Найдите строку с вашим user_id</li>\n";
echo "<li>Используйте этот ID в URL: <code>?chat_id=YOUR_ID</code></li>\n";
echo "</ol>\n";

echo "</body></html>\n";
?>
