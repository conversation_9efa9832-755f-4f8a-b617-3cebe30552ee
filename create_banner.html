<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniQPaid Bot Banner Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            font-family: Arial, sans-serif;
            color: white;
        }
        
        .banner {
            width: 1200px;
            height: 600px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 0 50px rgba(83, 52, 131, 0.5);
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(0, 100, 255, 0.1) 0%, transparent 50%);
        }
        
        .banner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.03) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 30%, rgba(255, 0, 255, 0.03) 50%, transparent 70%);
        }
        
        .content {
            position: relative;
            z-index: 10;
            text-align: center;
        }
        
        .logo {
            font-size: 72px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #0066ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            letter-spacing: 3px;
        }
        
        .tagline {
            font-size: 36px;
            color: #00ffff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
            font-weight: 300;
            letter-spacing: 2px;
        }
        
        .subtitle {
            font-size: 24px;
            color: #ffffff;
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 1px;
        }
        
        .glow-effect {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .glow-1 {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .glow-2 {
            bottom: 10%;
            right: 10%;
            animation-delay: 2s;
            background: radial-gradient(circle, rgba(255, 0, 255, 0.2) 0%, transparent 70%);
        }
        
        .glow-3 {
            top: 50%;
            left: 5%;
            animation-delay: 4s;
            background: radial-gradient(circle, rgba(0, 100, 255, 0.2) 0%, transparent 70%);
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-20px) scale(1.1); }
        }
        
        .instructions {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            border-radius: 10px;
            border: 1px solid #333;
        }
        
        .instructions h3 {
            color: #00ffff;
            margin-top: 0;
        }
        
        .instructions code {
            background: #333;
            padding: 2px 6px;
            border-radius: 3px;
            color: #00ffff;
        }
        
        .download-btn {
            background: linear-gradient(45deg, #00ffff, #0066ff);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin: 20px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="banner" id="banner">
        <div class="glow-effect glow-1"></div>
        <div class="glow-effect glow-2"></div>
        <div class="glow-effect glow-3"></div>
        
        <div class="content">
            <div class="logo">UniQPaid</div>
            <div class="tagline">Earn Crypto</div>
            <div class="subtitle">Watch Ads • Get Coins • Withdraw Instantly</div>
        </div>
    </div>
    
    <div class="instructions">
        <h3>🎨 Как сохранить баннер как PNG:</h3>
        <ol>
            <li>Нажмите кнопку "Скачать PNG" ниже</li>
            <li>Или сделайте скриншот баннера выше</li>
            <li>Сохраните файл как <code>bot_welcome_banner.png</code></li>
            <li>Загрузите в папку <code>images/</code> на сервер</li>
            <li>Протестируйте бота!</li>
        </ol>
        
        <h3>📐 Технические характеристики:</h3>
        <ul>
            <li><strong>Размер:</strong> 1200x600px (идеально для Telegram)</li>
            <li><strong>Формат:</strong> PNG с прозрачностью</li>
            <li><strong>Стиль:</strong> Кибер-панк с неоновыми эффектами</li>
            <li><strong>Качество:</strong> Высокое разрешение, без размытий</li>
        </ul>
        
        <button class="download-btn" onclick="downloadBanner()">📥 Скачать PNG</button>
        <button class="download-btn" onclick="copyBanner()">📋 Копировать в буфер</button>
    </div>

    <script>
        function downloadBanner() {
            const banner = document.getElementById('banner');
            
            // Используем html2canvas для создания PNG
            if (typeof html2canvas !== 'undefined') {
                html2canvas(banner, {
                    width: 1200,
                    height: 600,
                    scale: 2, // Для высокого качества
                    backgroundColor: null
                }).then(canvas => {
                    const link = document.createElement('a');
                    link.download = 'bot_welcome_banner.png';
                    link.href = canvas.toDataURL('image/png');
                    link.click();
                });
            } else {
                alert('Для скачивания нужна библиотека html2canvas. Сделайте скриншот баннера вручную.');
            }
        }
        
        function copyBanner() {
            const banner = document.getElementById('banner');
            
            if (typeof html2canvas !== 'undefined') {
                html2canvas(banner, {
                    width: 1200,
                    height: 600,
                    scale: 2
                }).then(canvas => {
                    canvas.toBlob(blob => {
                        const item = new ClipboardItem({ 'image/png': blob });
                        navigator.clipboard.write([item]).then(() => {
                            alert('Баннер скопирован в буфер обмена!');
                        });
                    });
                });
            } else {
                alert('Функция копирования недоступна. Сделайте скриншот вручную.');
            }
        }
    </script>
    
    <!-- Подключаем html2canvas для скачивания -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>
