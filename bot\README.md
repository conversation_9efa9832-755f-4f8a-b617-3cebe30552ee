# Telegram Bot для UniQPaid

## Установка

1. Загрузите все файлы из папки `bot/` на сервер в папку `bot/`
2. Убедитесь, что файл `bot_welcome_banner.svg` находится в папке `images/`
3. Откройте в браузере: `https://app.uniqpaid.com/test2/bot/set_webhook.php`
4. Проверьте, что webhook установлен успешно

## Файлы

- `config.php` - конфигурация бота
- `webhook.php` - основной обработчик webhook
- `set_webhook.php` - установка webhook

## Настройки

В файле `config.php` настроены:
- Токен бота: `8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA`
- URL webhook: `https://app.uniqpaid.com/test2/bot/webhook.php`
- URL приложения: `https://app.uniqpaid.com/test2/`
- Имя бота: `uniqpaid_paid_bot`

## Тестирование

После установки webhook:
1. Найдите бота в Telegram: @uniqpaid_paid_bot
2. Отправьте команду `/start`
3. Должна появиться картинка с приветственным сообщением и кнопками

## Логи

Логи бота сохраняются в файл `bot/bot.log`
