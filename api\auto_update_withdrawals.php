<?php
/**
 * auto_update_withdrawals.php
 * Автоматическое обновление статусов выплат
 * Этот файл должен вызываться по cron каждые 5-10 минут
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

error_log("auto_update_withdrawals INFO: Запуск автоматического обновления статусов выплат");

try {
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }
    
    $totalChecked = 0;
    $totalUpdated = 0;
    $statusChanges = [];
    
    // Проходим по всем пользователям
    foreach ($userData as $userId => $user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        $userUpdated = false;
        
        // Проверяем каждую выплату пользователя
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';
            
            // Пропускаем уже завершенные выплаты
            if (in_array($currentStatus, ['finished', 'completed', 'failed', 'cancelled', 'expired', 'refunded'])) {
                continue;
            }
            
            if (!$payoutId) {
                error_log("auto_update_withdrawals WARNING: Выплата без ID: " . json_encode($withdrawal));
                continue;
            }
            
            $totalChecked++;
            
            try {
                // Проверяем статус через NOWPayments API
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = $statusResponse['status'];
                    
                    // Если статус изменился
                    if ($newStatus !== $currentStatus) {
                        $oldStatus = $currentStatus;
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        
                        // Добавляем дополнительную информацию из API
                        if (isset($statusResponse['amount'])) {
                            $withdrawal['actual_amount'] = $statusResponse['amount'];
                        }
                        if (isset($statusResponse['fee'])) {
                            $withdrawal['actual_fee'] = $statusResponse['fee'];
                        }
                        if (isset($statusResponse['hash'])) {
                            $withdrawal['transaction_hash'] = $statusResponse['hash'];
                        }
                        if (isset($statusResponse['network'])) {
                            $withdrawal['network'] = $statusResponse['network'];
                        }
                        
                        $userUpdated = true;
                        $totalUpdated++;
                        
                        $statusChanges[] = [
                            'user_id' => $userId,
                            'withdrawal_id' => $payoutId,
                            'old_status' => $oldStatus,
                            'new_status' => $newStatus,
                            'amount' => $withdrawal['coins_amount'] ?? 0,
                            'currency' => $withdrawal['currency'] ?? 'unknown',
                            'timestamp' => time()
                        ];
                        
                        error_log("auto_update_withdrawals INFO: Пользователь {$userId}, выплата {$payoutId}: {$oldStatus} -> {$newStatus}");
                        
                        // Обрабатываем специальные статусы
                        handleStatusChange($userId, $withdrawal, $oldStatus, $newStatus, $userData);
                    }
                }
                
                // Небольшая задержка между запросами
                usleep(100000); // 0.1 секунды
                
            } catch (Exception $e) {
                error_log("auto_update_withdrawals WARNING: Ошибка проверки статуса {$payoutId}: " . $e->getMessage());
                continue;
            }
        }
        
        // Сохраняем данные пользователя если были изменения
        if ($userUpdated) {
            $userData[$userId]['withdrawals'] = $user['withdrawals'];
        }
    }
    
    // Сохраняем все изменения
    if ($totalUpdated > 0) {
        if (saveUserData($userData)) {
            error_log("auto_update_withdrawals SUCCESS: Обновлено {$totalUpdated} из {$totalChecked} выплат");
        } else {
            error_log("auto_update_withdrawals ERROR: Не удалось сохранить обновленные данные");
        }
    } else {
        error_log("auto_update_withdrawals INFO: Проверено {$totalChecked} выплат, обновлений нет");
    }
    
    // Возвращаем результат (для веб-вызовов)
    if (isset($_GET['web']) || isset($_POST['web'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'checked' => $totalChecked,
            'updated' => $totalUpdated,
            'status_changes' => $statusChanges,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
} catch (Exception $e) {
    error_log("auto_update_withdrawals CRITICAL ERROR: " . $e->getMessage());
    
    if (isset($_GET['web']) || isset($_POST['web'])) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}

/**
 * Обрабатывает изменение статуса выплаты
 */
function handleStatusChange($userId, &$withdrawal, $oldStatus, $newStatus, &$userData) {
    switch ($newStatus) {
        case 'finished':
        case 'completed':
            // Выплата успешно завершена
            error_log("auto_update_withdrawals SUCCESS: Выплата {$withdrawal['payout_id']} для пользователя {$userId} успешно завершена");
            
            // Можно добавить логику для уведомлений пользователя
            break;
            
        case 'failed':
        case 'cancelled':
        case 'expired':
            // Выплата не удалась - возвращаем средства
            $refundAmount = $withdrawal['coins_amount'] ?? 0;
            if ($refundAmount > 0) {
                $userData[$userId]['balance'] = ($userData[$userId]['balance'] ?? 0) + $refundAmount;
                error_log("auto_update_withdrawals INFO: Возвращено {$refundAmount} монет пользователю {$userId} из-за неудачной выплаты");
                
                // Добавляем информацию о возврате
                $withdrawal['refunded'] = true;
                $withdrawal['refund_amount'] = $refundAmount;
                $withdrawal['refund_date'] = date('Y-m-d H:i:s');
            }
            break;
            
        case 'sending':
        case 'confirming':
            // Выплата в процессе - просто логируем
            error_log("auto_update_withdrawals INFO: Выплата {$withdrawal['payout_id']} для пользователя {$userId} в процессе обработки");
            break;
    }
}
?>
