# Проверка безопасности после установки .htaccess

## Быстрая проверка

### ❌ Эти URL должны быть НЕДОСТУПНЫ (403 Forbidden):

```
https://app.uniqpaid.com/test2/api/config.php
https://app.uniqpaid.com/test2/api/user_data.json
https://app.uniqpaid.com/test2/api/db_mock.php
https://app.uniqpaid.com/test2/api/security.php
https://app.uniqpaid.com/test2/bot/config.php
https://app.uniqpaid.com/test2/includes/
https://app.uniqpaid.com/test2/instruction/
https://app.uniqpaid.com/test2/api/admin/
https://app.uniqpaid.com/test2/.htaccess
```

### ✅ Эти URL должны быть ДОСТУПНЫ:

```
https://app.uniqpaid.com/test2/
https://app.uniqpaid.com/test2/api/getUserData.php
https://app.uniqpaid.com/test2/api/recordAdView.php
https://app.uniqpaid.com/test2/images/bot_welcome_banner.svg
https://app.uniqpaid.com/test2/locales/ru.json
https://app.uniqpaid.com/test2/bot/webhook.php
https://app.uniqpaid.com/test2/main.js
https://app.uniqpaid.com/test2/cyberpunk-styles.css
```

## Файлы для загрузки на сервер

Загрузите следующие .htaccess файлы:

1. `/.htaccess` → в корень проекта
2. `/api/.htaccess` → в папку api/
3. `/api/admin/.htaccess` → в папку api/admin/
4. `/bot/.htaccess` → в папку bot/
5. `/includes/.htaccess` → в папку includes/
6. `/instruction/.htaccess` → в папку instruction/
7. `/locales/.htaccess` → в папку locales/

## После загрузки проверьте:

1. **Приложение работает** - откройте https://app.uniqpaid.com/test2/
2. **API работает** - проверьте функции в приложении
3. **Бот работает** - отправьте /start боту
4. **Защищенные файлы недоступны** - проверьте список выше
