# Главный .htaccess для UniQPaid
# Общие настройки безопасности и производительности

# Включаем mod_rewrite
RewriteEngine On

# Защита от просмотра .htaccess файлов
<Files ".htaccess">
    Order Deny,Allow
    Deny from all
</Files>

# Защита от просмотра других системных файлов
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.bak">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.backup">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.zip">
    Order Deny,Allow
    Deny from all
</Files>

# Защита от доступа к Git файлам
<Files ".git*">
    Order Deny,Allow
    Deny from all
</Files>

# Защита от доступа к файлам редакторов
<Files "*~">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.swp">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем просмотр директорий
Options -Indexes

# Защита от некоторых атак
<IfModule mod_headers.c>
    # Защита от XSS
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Защита от MIME-type sniffing
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' data: https:; font-src 'self' data: https:;"
</IfModule>

# Кэширование статических файлов
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType application/json "access plus 1 day"
</IfModule>

# Сжатие файлов
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Разрешаем доступ к основным файлам приложения
<Files "index.html">
    Order Allow,Deny
    Allow from all
</Files>

<Files "main.js">
    Order Allow,Deny
    Allow from all
</Files>

<Files "cyberpunk-styles.css">
    Order Allow,Deny
    Allow from all
</Files>

# Разрешаем доступ к изображениям
<Files "images/*">
    Order Allow,Deny
    Allow from all
</Files>

# Разрешаем доступ к JS файлам
<Files "js/*">
    Order Allow,Deny
    Allow from all
</Files>
