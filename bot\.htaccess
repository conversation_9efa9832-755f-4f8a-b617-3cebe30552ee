# Защита bot директории

# Запрещаем доступ ко всем файлам по умолчанию
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Разрешаем доступ только к webhook.php для Telegram
<Files "webhook.php">
    Order Allow,Deny
    Allow from all
    
    # Дополнительная защита - разрешаем только POST запросы от Telegram
    <RequireAll>
        Require method POST
    </RequireAll>
</Files>

# Разрешаем доступ к set_webhook.php только для администрирования
<Files "set_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

# Запрещаем доступ к конфигурационным файлам
<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к документации
<Files "*.md">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к логам
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем просмотр директории
Options -Indexes
