<?php
echo "=== ПРОСТОЙ ТЕСТ ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
echo "PHP версия: " . phpversion() . "\n";

// Проверяем файлы в api
$apiDir = __DIR__ . '/api/';
echo "Папка API: " . $apiDir . "\n";

if (is_dir($apiDir)) {
    echo "✅ Папка api существует\n";
    $files = scandir($apiDir);
    echo "Файлы в api:\n";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "- {$file}\n";
        }
    }
} else {
    echo "❌ Папка api не найдена\n";
}

// Проверяем user_data.json
$userDataFile = $apiDir . 'user_data.json';
if (file_exists($userDataFile)) {
    echo "✅ user_data.json найден (" . filesize($userDataFile) . " байт)\n";
    
    $userData = json_decode(file_get_contents($userDataFile), true);
    if ($userData) {
        echo "✅ JSON корректный, пользователей: " . count($userData) . "\n";
        
        // Анализируем выплаты
        $totalWithdrawals = 0;
        $pendingWithdrawals = 0;
        
        foreach ($userData as $userId => $user) {
            if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
                foreach ($user['withdrawals'] as $withdrawal) {
                    $totalWithdrawals++;
                    if (($withdrawal['status'] ?? 'unknown') === 'pending') {
                        $pendingWithdrawals++;
                    }
                }
            }
        }
        
        echo "📊 Всего выплат: {$totalWithdrawals}\n";
        echo "⏳ В ожидании: {$pendingWithdrawals}\n";
        
    } else {
        echo "❌ Ошибка парсинга JSON\n";
    }
} else {
    echo "❌ user_data.json не найден\n";
}

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
