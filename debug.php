<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== ОТЛАДКА СИСТЕМЫ ВЫПЛАТ ===\n\n";

// 1. Проверяем структуру
echo "1. СТРУКТУРА ФАЙЛОВ:\n";
echo "Текущая директория: " . __DIR__ . "\n";

$apiFiles = [
    'api/config.php',
    'api/functions.php',
    'api/NOWPaymentsAPI.php',
    'api/user_data.json',
    'api/requestWithdrawal.php',
    'api/getWithdrawalHistory.php'
];

foreach ($apiFiles as $file) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✅ {$file} - OK (" . filesize($fullPath) . " байт)\n";
    } else {
        echo "❌ {$file} - НЕ НАЙДЕН\n";
    }
}

// 2. Проверяем user_data.json
echo "\n2. АНАЛИЗ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ:\n";
$userDataPath = __DIR__ . '/api/user_data.json';

if (file_exists($userDataPath)) {
    $content = file_get_contents($userDataPath);
    $userData = json_decode($content, true);
    
    if ($userData && is_array($userData)) {
        echo "✅ JSON корректный\n";
        echo "👥 Пользователей: " . count($userData) . "\n";
        
        $stats = [
            'total_withdrawals' => 0,
            'pending_withdrawals' => 0,
            'missing_payout_ids' => 0,
            'users_with_withdrawals' => 0
        ];
        
        foreach ($userData as $userId => $user) {
            if (isset($user['withdrawals']) && is_array($user['withdrawals']) && !empty($user['withdrawals'])) {
                $stats['users_with_withdrawals']++;
                
                foreach ($user['withdrawals'] as $withdrawal) {
                    $stats['total_withdrawals']++;
                    
                    $status = $withdrawal['status'] ?? 'unknown';
                    if ($status === 'pending') {
                        $stats['pending_withdrawals']++;
                    }
                    
                    $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
                    if (!$payoutId) {
                        $stats['missing_payout_ids']++;
                    }
                }
            }
        }
        
        echo "📊 СТАТИСТИКА:\n";
        echo "- Всего выплат: {$stats['total_withdrawals']}\n";
        echo "- В ожидании: {$stats['pending_withdrawals']}\n";
        echo "- Без payout_id: {$stats['missing_payout_ids']}\n";
        echo "- Пользователей с выплатами: {$stats['users_with_withdrawals']}\n";
        
        // Показываем примеры проблемных выплат
        if ($stats['missing_payout_ids'] > 0 || $stats['pending_withdrawals'] > 0) {
            echo "\n🔍 ПРИМЕРЫ ПРОБЛЕМ:\n";
            $shown = 0;
            foreach ($userData as $userId => $user) {
                if ($shown >= 3) break;
                
                if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
                    foreach ($user['withdrawals'] as $index => $withdrawal) {
                        if ($shown >= 3) break;
                        
                        $status = $withdrawal['status'] ?? 'unknown';
                        $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
                        $amount = $withdrawal['coins_amount'] ?? 0;
                        
                        if (!$payoutId || $status === 'pending') {
                            echo "- Пользователь {$userId}, выплата #{$index}:\n";
                            echo "  Сумма: {$amount} монет\n";
                            echo "  Статус: {$status}\n";
                            echo "  Payout ID: " . ($payoutId ?: 'ОТСУТСТВУЕТ') . "\n";
                            $shown++;
                        }
                    }
                }
            }
        }
        
    } else {
        echo "❌ Ошибка парсинга JSON\n";
        echo "Первые 200 символов:\n" . substr($content, 0, 200) . "\n";
    }
} else {
    echo "❌ Файл user_data.json не найден\n";
}

// 3. Проверяем конфигурацию
echo "\n3. ПРОВЕРКА КОНФИГУРАЦИИ:\n";
if (file_exists(__DIR__ . '/api/config.php')) {
    try {
        include_once __DIR__ . '/api/config.php';
        
        $requiredConstants = [
            'NOWPAYMENTS_API_KEY',
            'NOWPAYMENTS_PUBLIC_KEY',
            'NOWPAYMENTS_IPN_SECRET',
            'NOWPAYMENTS_API_URL'
        ];
        
        foreach ($requiredConstants as $const) {
            if (defined($const)) {
                $value = constant($const);
                echo "✅ {$const}: " . substr($value, 0, 10) . "...\n";
            } else {
                echo "❌ {$const}: НЕ ОПРЕДЕЛЕНА\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Ошибка загрузки config.php: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ config.php не найден\n";
}

// 4. Быстрое исправление
echo "\n4. БЫСТРОЕ ИСПРАВЛЕНИЕ:\n";

if (isset($userData) && is_array($userData) && $stats['missing_payout_ids'] > 0) {
    echo "🔧 Исправляем отсутствующие payout_id...\n";
    
    $fixed = 0;
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals'])) continue;
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $id = $withdrawal['id'] ?? null;
            
            if (!$payoutId && $id) {
                $withdrawal['payout_id'] = $id;
                $fixed++;
                echo "✅ Пользователь {$userId}: добавлен payout_id = {$id}\n";
            } elseif (!$payoutId && !$id) {
                $tempId = 'temp_' . $userId . '_' . $index . '_' . time();
                $withdrawal['payout_id'] = $tempId;
                $withdrawal['id'] = $tempId;
                $fixed++;
                echo "⚠️ Пользователь {$userId}: создан временный ID = {$tempId}\n";
            }
        }
    }
    
    if ($fixed > 0) {
        $newContent = json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        if (file_put_contents($userDataPath, $newContent)) {
            echo "✅ Сохранено {$fixed} исправлений в user_data.json\n";
        } else {
            echo "❌ Не удалось сохранить исправления\n";
        }
    }
}

echo "\n=== ОТЛАДКА ЗАВЕРШЕНА ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
?>
