# Защита locales директории

# Запрещаем доступ ко всем файлам по умолчанию
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Разрешаем доступ к JSON файлам локализации для AJAX запросов
<Files "*.json">
    Order Allow,Deny
    Allow from all
    
    # Устанавливаем правильный Content-Type для JSON
    <IfModule mod_headers.c>
        Header set Content-Type "application/json; charset=utf-8"
    </IfModule>
</Files>

# Запрещаем просмотр директории
Options -Indexes
